import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient()

    console.log('Starting complete database setup...')

    // Test basic connection first
    const { data: testData, error: testError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .limit(1)

    if (testError) {
      console.error('Database connection test failed:', testError)
      return NextResponse.json({ 
        error: 'Database connection failed', 
        details: testError,
        suggestion: 'Please check your Supabase configuration and ensure the service role key has proper permissions.'
      }, { status: 500 })
    }

    console.log('Database connection successful')

    // Check if tables already exist
    const { data: existingTables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['users', 'services', 'appointments'])

    if (tablesError) {
      console.error('Error checking existing tables:', tablesError)
    }

    const tablesExist = existingTables && existingTables.length > 0
    console.log('Existing tables found:', tablesExist ? existingTables.map(t => t.table_name) : 'none')

    if (tablesExist) {
      // Tables already exist, just verify they're working
      const { data: servicesData, error: servicesError } = await supabase
        .from('services')
        .select('count')
        .limit(1)

      if (!servicesError) {
        return NextResponse.json({ 
          success: true, 
          message: 'Database is already set up and working properly',
          tablesFound: existingTables.map(t => t.table_name)
        })
      }
    }

    // If we get here, we need to set up the database
    return NextResponse.json({ 
      success: false,
      error: 'Database setup required',
      message: 'Please run the complete-setup.sql script in your Supabase SQL Editor',
      instructions: [
        '1. Go to your Supabase dashboard',
        '2. Navigate to the SQL Editor',
        '3. Copy and paste the contents of scripts/complete-setup.sql',
        '4. Run the script',
        '5. Try this setup endpoint again'
      ]
    }, { status: 400 })

  } catch (error) {
    console.error('Complete setup error:', error)
    return NextResponse.json({ 
      error: 'Setup failed', 
      details: error instanceof Error ? error.message : 'Unknown error',
      suggestion: 'Please check your Supabase configuration and try running the SQL script manually.'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient()

    // Check database status
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['users', 'services', 'appointments', 'homepage_stats', 'customer_reviews'])

    if (tablesError) {
      return NextResponse.json({
        status: 'error',
        message: 'Cannot check database status',
        error: tablesError
      })
    }

    const requiredTables = ['users', 'services', 'appointments', 'homepage_stats', 'customer_reviews']
    const existingTables = tables?.map(t => t.table_name) || []
    const missingTables = requiredTables.filter(table => !existingTables.includes(table))

    if (missingTables.length === 0) {
      // Check if we can actually query the tables
      try {
        const { data: servicesData } = await supabase.from('services').select('count').limit(1)
        const { data: statsData } = await supabase.from('homepage_stats').select('count').limit(1)
        
        return NextResponse.json({
          status: 'ready',
          message: 'Database is fully set up and functional',
          tables: existingTables,
          dataCheck: {
            services: servicesData ? 'accessible' : 'empty',
            stats: statsData ? 'accessible' : 'empty'
          }
        })
      } catch (queryError) {
        return NextResponse.json({
          status: 'partial',
          message: 'Tables exist but may have permission issues',
          tables: existingTables,
          error: queryError
        })
      }
    } else {
      return NextResponse.json({
        status: 'incomplete',
        message: 'Database setup is incomplete',
        existingTables,
        missingTables,
        instructions: [
          '1. Go to your Supabase dashboard',
          '2. Navigate to the SQL Editor',
          '3. Copy and paste the contents of scripts/complete-setup.sql',
          '4. Run the script'
        ]
      })
    }

  } catch (error) {
    return NextResponse.json({
      status: 'error',
      message: 'Failed to check database status',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
