"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { AppHeader } from "@/components/ui/app-header"

export default function SetupPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<string[]>([])

  const addResult = (message: string) => {
    setResults(prev => [...prev, message])
  }

  const setupDatabase = async () => {
    setLoading(true)
    addResult("Setting up database tables...")
    
    try {
      const response = await fetch('/api/setup-database', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      const result = await response.json()
      
      if (response.ok) {
        addResult("✅ Database tables created successfully")
      } else {
        addResult(`❌ Database setup failed: ${result.error}`)
      }
    } catch (error) {
      addResult(`❌ Database setup error: ${error}`)
    }
  }

  const setupRLS = async () => {
    addResult("Setting up RLS policies...")
    
    try {
      const response = await fetch('/api/setup-rls', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      const result = await response.json()
      
      if (response.ok) {
        addResult("✅ RLS policies created successfully")
      } else {
        addResult(`❌ RLS setup failed: ${result.error}`)
      }
    } catch (error) {
      addResult(`❌ RLS setup error: ${error}`)
    }
  }

  const setupStorage = async () => {
    addResult("Setting up storage buckets...")
    
    try {
      const response = await fetch('/api/setup-storage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      const result = await response.json()
      
      if (response.ok) {
        addResult("✅ Storage buckets created successfully")
      } else {
        addResult(`❌ Storage setup failed: ${result.error}`)
      }
    } catch (error) {
      addResult(`❌ Storage setup error: ${error}`)
    }
    
    setLoading(false)
  }

  const runFullSetup = async () => {
    setResults([])
    setLoading(true)
    
    await setupDatabase()
    await setupRLS()
    await setupStorage()
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <AppHeader title="Database Setup" showBack />
      
      <div className="p-4 space-y-6">
        <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
          <CardContent className="p-6">
            <h2 className="text-xl font-bold text-pink-800 mb-4">Database Setup</h2>
            <p className="text-gray-600 mb-6">
              This will create all necessary database tables, RLS policies, and storage buckets.
            </p>
            
            <div className="space-y-3">
              <Button 
                onClick={runFullSetup}
                disabled={loading}
                className="w-full bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
              >
                {loading ? "Setting up..." : "Run Full Setup"}
              </Button>
              
              <div className="grid grid-cols-3 gap-2">
                <Button 
                  onClick={setupDatabase}
                  disabled={loading}
                  variant="outline"
                  size="sm"
                  className="border-pink-200"
                >
                  Database
                </Button>
                <Button 
                  onClick={setupRLS}
                  disabled={loading}
                  variant="outline"
                  size="sm"
                  className="border-pink-200"
                >
                  RLS Policies
                </Button>
                <Button 
                  onClick={setupStorage}
                  disabled={loading}
                  variant="outline"
                  size="sm"
                  className="border-pink-200"
                >
                  Storage
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {results.length > 0 && (
          <Card className="border-gray-200">
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-900 mb-3">Setup Results</h3>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {results.map((result, index) => (
                  <div 
                    key={index} 
                    className={`text-sm p-2 rounded ${
                      result.includes('✅') 
                        ? 'bg-green-50 text-green-700' 
                        : result.includes('❌')
                        ? 'bg-red-50 text-red-700'
                        : 'bg-blue-50 text-blue-700'
                    }`}
                  >
                    {result}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
