import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Sparkles, Crown, Heart, ChevronRight } from "lucide-react"
import Link from "next/link"
import { AppHeader } from "@/components/ui/app-header"

// Type definitions
interface Service {
  name: string;
  price: string;
  duration: string;
  description: string;
  image: string;
  popular?: boolean;
  premium?: boolean;
}

interface ServiceCategory {
  category: string;
  icon: React.ReactNode;
  color: string;
  services: Service[];
}

const serviceCategories: ServiceCategory[] = [
  {
    category: "Classic Manicures",
    icon: <Heart className="h-5 w-5" />,
    color: "from-pink-500 to-rose-500",
    services: [
      {
        name: "Classic Manicure",
        price: "$35",
        duration: "45 min",
        description: "Traditional nail care with polish",
        image: "/placeholder.svg?height=100&width=150&text=Classic",
      },
      {
        name: "French Manicure",
        price: "$40",
        duration: "50 min",
        description: "Timeless French tip design",
        image: "/placeholder.svg?height=100&width=150&text=French",
      },
      {
        name: "Gel Manicure",
        price: "$45",
        duration: "60 min",
        description: "Long-lasting gel polish",
        image: "/placeholder.svg?height=100&width=150&text=Gel",
        popular: true,
      },
    ],
  },
  {
    category: "Nail Art & Design",
    icon: <Sparkles className="h-5 w-5" />,
    color: "from-amber-500 to-orange-500",
    services: [
      {
        name: "Simple Nail Art",
        price: "$55",
        duration: "60 min",
        description: "Basic designs and patterns",
        image: "/placeholder.svg?height=100&width=150&text=Simple+Art",
      },
      {
        name: "Intricate Nail Art",
        price: "$75",
        duration: "90 min",
        description: "Complex artistic designs",
        image: "/placeholder.svg?height=100&width=150&text=Intricate+Art",
        popular: true,
      },
      {
        name: "3D Nail Art",
        price: "$85",
        duration: "100 min",
        description: "Dimensional nail sculptures",
        image: "/placeholder.svg?height=100&width=150&text=3D+Art",
      },
    ],
  },
  {
    category: "Luxury Treatments",
    icon: <Crown className="h-5 w-5" />,
    color: "from-purple-500 to-pink-500",
    services: [
      {
        name: "Diamond Manicure",
        price: "$120",
        duration: "90 min",
        description: "Ultimate luxury experience",
        image: "/placeholder.svg?height=100&width=150&text=Diamond",
        premium: true,
      },
      {
        name: "Gold Leaf Treatment",
        price: "$100",
        duration: "80 min",
        description: "24k gold leaf application",
        image: "/placeholder.svg?height=100&width=150&text=Gold+Leaf",
      },
    ],
  },
]

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <AppHeader title="Services" showBack />

      <div className="p-4 space-y-6">
        {serviceCategories.map((category, categoryIndex) => (
          <div key={categoryIndex}>
            {/* Category Header */}
            <div className="flex items-center mb-4">
              <div className={`bg-gradient-to-r ${category.color} p-2 rounded-lg text-white mr-3`}>{category.icon}</div>
              <h2 className="text-lg font-bold text-gray-900">{category.category}</h2>
            </div>

            {/* Services in Category */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {category.services.map((service, serviceIndex) => (
                <Card key={serviceIndex} className="border-0 shadow-sm overflow-hidden">
                  <CardContent className="p-0">
                    <div className="flex">
                      <img
                        src={service.image || "/placeholder.svg"}
                        alt={service.name}
                        className="w-20 h-20 object-cover"
                      />
                      <div className="flex-1 p-3">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="font-semibold text-gray-900 text-sm">{service.name}</h3>
                          <div className="flex flex-col items-end">
                            {service.popular && <Badge className="bg-pink-500 text-white text-xs mb-1">Popular</Badge>}
                            {service.premium && <Badge className="bg-amber-500 text-white text-xs mb-1">Premium</Badge>}
                          </div>
                        </div>
                        <p className="text-xs text-gray-600 mb-2">{service.description}</p>
                        <div className="flex items-center justify-between">
                          <div className="flex flex-col">
                            <span className="text-lg font-bold text-pink-600">{service.price}</span>
                            <span className="text-xs text-gray-500">{service.duration}</span>
                          </div>
                          <Button size="sm" className="bg-pink-500 hover:bg-pink-600 text-white text-xs px-2 py-1" asChild>
                            <Link href="/booking">Book</Link>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}

        {/* Membership Card */}
        <Card className="border-0 shadow-sm bg-gradient-to-r from-pink-500 to-amber-500 text-white">
          <CardContent className="p-6 text-center">
            <Crown className="h-12 w-12 mx-auto mb-4" />
            <h3 className="text-xl font-bold mb-2">VIP Membership</h3>
            <p className="text-pink-100 mb-4">Get 20% off all services + exclusive perks</p>
            <Button className="bg-white text-pink-600 hover:bg-pink-50 rounded-full font-semibold">
              Learn More
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
