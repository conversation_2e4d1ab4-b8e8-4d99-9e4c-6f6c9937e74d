"use client"

import { useEffect, useState, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { supabase } from '@/lib/supabase'

function AuthCallbackContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [status, setStatus] = useState('Processing authentication...')

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        setStatus('Verifying authentication...')

        // Handle the OAuth callback
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Auth callback error:', error)
          setStatus('Authentication failed')
          setTimeout(() => router.push('/?error=auth_error'), 2000)
          return
        }

        if (data.session?.user) {
          setStatus('Creating user profile...')

          // Check if user profile exists, if not create it
          const { data: existingUser, error: fetchError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.session.user.id)
            .single()

          if (fetchError && fetchError.code === 'PGRST116') {
            // User doesn't exist, create profile
            const userData = {
              id: data.session.user.id,
              email: data.session.user.email!,
              first_name: data.session.user.user_metadata?.first_name ||
                         data.session.user.user_metadata?.full_name?.split(' ')[0] ||
                         'User',
              last_name: data.session.user.user_metadata?.last_name ||
                        data.session.user.user_metadata?.full_name?.split(' ').slice(1).join(' ') ||
                        '',
              avatar_url: data.session.user.user_metadata?.avatar_url ||
                         data.session.user.user_metadata?.picture,
              phone: data.session.user.user_metadata?.phone || '',
              is_admin: false,
              is_member: false,
              subscribe_announcements: false,
              email_verified: data.session.user.email_confirmed_at ? true : false
            }

            const { error: insertError } = await supabase
              .from('users')
              .insert(userData)

            if (insertError) {
              console.error('Error creating user profile:', insertError)
              setStatus('Failed to create user profile')
              setTimeout(() => router.push('/?error=profile_creation_failed'), 2000)
              return
            }
          } else if (fetchError) {
            console.error('Error fetching user profile:', fetchError)
            setStatus('Failed to verify user profile')
            setTimeout(() => router.push('/?error=profile_fetch_failed'), 2000)
            return
          }

          setStatus('Authentication successful! Redirecting...')

          // Redirect to intended page or home
          const redirectTo = searchParams.get('redirectTo') || '/'
          setTimeout(() => router.push(redirectTo), 1000)
        } else {
          setStatus('No session found')
          setTimeout(() => router.push('/?error=no_session'), 2000)
        }
      } catch (error) {
        console.error('Auth callback error:', error)
        setStatus('Authentication failed')
        setTimeout(() => router.push('/?error=callback_error'), 2000)
      }
    }

    handleAuthCallback()
  }, [router, searchParams])

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4"></div>
        <p className="text-gray-600 text-lg mb-2">{status}</p>
        <p className="text-gray-500 text-sm">Please wait while we complete your sign in...</p>
      </div>
    </div>
  )
}

export default function AuthCallback() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg mb-2">Loading...</p>
          <p className="text-gray-500 text-sm">Please wait while we complete your sign in...</p>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  )
}
