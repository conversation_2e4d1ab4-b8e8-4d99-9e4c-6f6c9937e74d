"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import MetallicPaint from "@/src/blocks/Animations/MetallicPaint/MetallicPaint"
import styles from "./animated-logo.module.css"

interface AnimatedLogoProps {
  className?: string
  width?: number
  height?: number
  showAnimation?: boolean
}

export function AnimatedLogo({
  className = "",
  width = 120,
  height = 60,
  showAnimation = true
}: AnimatedLogoProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [imageData, setImageData] = useState<ImageData | null>(null)
  const [useMetallic, setUseMetallic] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    // Preload the image and process it for metallic effect
    const img = new window.Image()
    img.crossOrigin = "anonymous"
    img.onload = () => {
      setIsLoaded(true)

      if (showAnimation) {
        // Process image for metallic effect
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')

        if (ctx) {
          canvas.width = img.width
          canvas.height = img.height
          ctx.drawImage(img, 0, 0)

          try {
            const imgData = ctx.getImageData(0, 0, canvas.width, canvas.height)
            setImageData(imgData)
            setUseMetallic(true)
          } catch (error) {
            console.log('Could not process image for metallic effect, using fallback')
            setUseMetallic(false)
          }
        }
      }
    }
    img.src = "/images/Nails by Lingg.PNG"
  }, [showAnimation])

  return (
    <div
      className={`relative overflow-hidden ${className}`}
      style={{ width, height }}
    >
      {showAnimation && useMetallic && imageData ? (
        // Metallic paint effect
        <div style={{ width, height }}>
          <MetallicPaint
            imageData={imageData}
            params={{
              patternScale: 1.5,
              refraction: 0.01,
              edge: 0.8,
              patternBlur: 0.003,
              liquid: 0.05,
              speed: 0.2,
            }}
          />
        </div>
      ) : (
        // Fallback to regular logo with shimmer
        <>
          <Image
            src="/images/Nails by Lingg.PNG"
            alt="Nails by Lingg"
            width={width}
            height={height}
            className={`object-contain transition-opacity duration-500 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            priority
          />

          {/* Simple metallic overlay animation fallback */}
          {showAnimation && isLoaded && (
            <div
              className={`absolute inset-0 opacity-20 ${styles.shimmer}`}
              style={{
                background: `linear-gradient(
                  45deg,
                  transparent 30%,
                  rgba(255, 215, 0, 0.6) 50%,
                  transparent 70%
                )`
              }}
            />
          )}
        </>
      )}
    </div>
  )
}
