"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import styles from "./animated-logo.module.css"

interface AnimatedLogoProps {
  className?: string
  width?: number
  height?: number
  showAnimation?: boolean
}

export function AnimatedLogo({ 
  className = "", 
  width = 120, 
  height = 60,
  showAnimation = true 
}: AnimatedLogoProps) {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Preload the image
    const img = new window.Image()
    img.onload = () => setIsLoaded(true)
    img.src = "/images/Nails by Lingg.PNG"
  }, [])

  return (
    <div 
      className={`relative overflow-hidden ${className}`}
      style={{ width, height }}
    >
      {/* Base logo */}
      <Image
        src="/images/Nails by Lingg.PNG"
        alt="Nails by Lingg"
        width={width}
        height={height}
        className={`object-contain transition-opacity duration-500 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        priority
      />
      
      {/* Simple metallic overlay animation */}
      {showAnimation && isLoaded && (
        <div
          className={`absolute inset-0 opacity-20 ${styles.shimmer}`}
          style={{
            background: `linear-gradient(
              45deg,
              transparent 30%,
              rgba(255, 215, 0, 0.6) 50%,
              transparent 70%
            )`
          }}
        />
      )}
      

    </div>
  )
}
